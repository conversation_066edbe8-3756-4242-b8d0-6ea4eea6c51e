# HTML综合练习案例

## 案例介绍
这是一个综合性的HTML练习案例，涵盖了HTML、CSS和JavaScript的基础知识，包括：
- HTML语义化结构
- 表单元素
- 多媒体元素
- CSS样式和布局
- JavaScript交互

## HTML代码

```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>综合练习案例</title>
    <style>
        /* CSS样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
        }

        header {
            background-color: #4a90e2;
            color: white;
            padding: 1rem;
            text-align: center;
        }

        nav {
            background-color: #333;
            padding: 0.5rem;
        }

        nav ul {
            list-style: none;
            display: flex;
            justify-content: center;
        }

        nav li {
            margin: 0 1rem;
        }

        nav a {
            color: white;
            text-decoration: none;
        }

        nav a:hover {
            text-decoration: underline;
        }

        .container {
            display: flex;
            flex-wrap: wrap;
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 1rem;
        }

        main {
            flex: 3;
            min-width: 300px;
            padding: 1rem;
        }

        aside {
            flex: 1;
            min-width: 250px;
            padding: 1rem;
            background-color: #f4f4f4;
            margin-left: 1rem;
        }

        section {
            margin-bottom: 2rem;
        }

        h2 {
            color: #4a90e2;
            margin-bottom: 1rem;
        }

        form {
            background-color: #f9f9f9;
            padding: 1rem;
            border-radius: 5px;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
        }

        input, select, textarea {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 3px;
        }

        button {
            background-color: #4a90e2;
            color: white;
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }

        button:hover {
            background-color: #357ae8;
        }

        .gallery {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .gallery img {
            width: 200px;
            height: 150px;
            object-fit: cover;
            border-radius: 5px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin: 1rem 0;
        }

        th, td {
            border: 1px solid #ddd;
            padding: 0.5rem;
            text-align: left;
        }

        th {
            background-color: #f4f4f4;
        }

        footer {
            background-color: #333;
            color: white;
            text-align: center;
            padding: 1rem;
            margin-top: 2rem;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                flex-direction: column;
            }

            aside {
                margin-left: 0;
                margin-top: 1rem;
            }

            nav ul {
                flex-direction: column;
                align-items: center;
            }

            nav li {
                margin: 0.5rem 0;
            }
        }
    </style>
</head>
<body>
    <header>
        <h1>我的个人网站</h1>
        <p>欢迎来到我的网站</p>
    </header>

    <nav>
        <ul>
            <li><a href="#home">首页</a></li>
            <li><a href="#about">关于我</a></li>
            <li><a href="#portfolio">作品集</a></li>
            <li><a href="#contact">联系我</a></li>
        </ul>
    </nav>

    <div class="container">
        <main>
            <section id="home">
                <h2>欢迎访问</h2>
                <p>这是一个HTML综合练习案例，展示了多种HTML元素和CSS样式。</p>
                
                <h3>图片画廊</h3>
                <div class="gallery">
                    <img src="https://picsum.photos/200/150?random=1" alt="示例图片1">
                    <img src="https://picsum.photos/200/150?random=2" alt="示例图片2">
                    <img src="https://picsum.photos/200/150?random=3" alt="示例图片3">
                </div>
            </section>

            <section id="about">
                <h2>关于我</h2>
                <p>我是一名前端开发学习者，正在努力提升自己的技能。</p>
                
                <h3>技能表</h3>
                <table>
                    <thead>
                        <tr>
                            <th>技能</th>
                            <th>熟练程度</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>HTML</td>
                            <td>熟练</td>
                        </tr>
                        <tr>
                            <td>CSS</td>
                            <td>熟练</td>
                        </tr>
                        <tr>
                            <td>JavaScript</td>
                            <td>中级</td>
                        </tr>
                    </tbody>
                </table>
            </section>

            <section id="contact">
                <h2>联系我</h2>
                <form id="contactForm">
                    <div class="form-group">
                        <label for="name">姓名:</label>
                        <input type="text" id="name" name="name" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="email">邮箱:</label>
                        <input type="email" id="email" name="email" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="gender">性别:</label>
                        <div>
                            <input type="radio" id="male" name="gender" value="male">
                            <label for="male">男</label>
                            <input type="radio" id="female" name="gender" value="female">
                            <label for="female">女</label>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="interests">兴趣爱好:</label>
                        <div>
                            <input type="checkbox" id="coding" name="interests" value="coding">
                            <label for="coding">编程</label>
                            <input type="checkbox" id="reading" name="interests" value="reading">
                            <label for="reading">阅读</label>
                            <input type="checkbox" id="music" name="interests" value="music">
                            <label for="music">音乐</label>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="country">国家:</label>
                        <select id="country" name="country">
                            <option value="china">中国</option>
                            <option value="usa">美国</option>
                            <option value="uk">英国</option>
                            <option value="japan">日本</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="message">留言:</label>
                        <textarea id="message" name="message" rows="5"></textarea>
                    </div>
                    
                    <button type="submit">提交</button>
                </form>
            </section>
        </main>

        <aside>
            <h2>侧边栏</h2>
            <p>这里是一些额外的信息。</p>
            
            <h3>音频播放器</h3>
            <audio controls>
                <source src="https://www.soundhelix.com/examples/mp3/SoundHelix-Song-1.mp3" type="audio/mpeg">
                您的浏览器不支持音频元素。
            </audio>
            
            <h3>视频播放器</h3>
            <video width="100%" controls>
                <source src="https://www.w3schools.com/html/mov_bbb.mp4" type="video/mp4">
                您的浏览器不支持视频元素。
            </video>
        </aside>
    </div>

    <footer>
        <p>&copy; 2023 我的个人网站. 版权所有.</p>
    </footer>

    <script>
        // JavaScript代码
        document.getElementById('contactForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // 获取表单数据
            const name = document.getElementById('name').value;
            const email = document.getElementById('email').value;
            
            // 简单验证
            if (name && email) {
                alert(`感谢您的提交，${name}！我们将会发送确认邮件到 ${email}`);
                // 这里可以添加实际的表单提交逻辑
            } else {
                alert('请填写必填字段！');
            }
        });
        
        // 添加一个简单的交互功能
        const navLinks = document.querySelectorAll('nav a');
        navLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const targetId = this.getAttribute('href');
                const targetSection = document.querySelector(targetId);
                
                if (targetSection) {
                    targetSection.scrollIntoView({ behavior: 'smooth' });
                }
            });
        });
    </script>
</body>
</html>
```

## 练习要点

1. **HTML结构**：
   - 使用了语义化标签：`<header>`, `<nav>`, `<main>`, `<section>`, `<aside>`, `<footer>`
   - 包含了多种表单元素：文本输入、单选按钮、复选框、下拉列表、文本域
   - 使用了多媒体元素：`<audio>` 和 `<video>`

2. **CSS样式**：
   - 使用了Flexbox布局实现响应式设计
   - 包含了媒体查询实现移动端适配
   - 使用了选择器、盒模型等基础知识

3. **JavaScript交互**：
   - 表单验证和提交处理
   - 导航链接的平滑滚动效果
   - 事件监听器的使用

## 练习建议

1. 将代码保存为HTML文件并在浏览器中打开查看效果
2. 尝试修改CSS样式，改变页面的外观
3. 添加更多的JavaScript交互功能
4. 尝试将CSS和JavaScript分离到独立的文件中
5. 添加更多的HTML元素和内容