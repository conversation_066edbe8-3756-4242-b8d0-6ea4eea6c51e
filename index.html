<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>欢迎来到苏溪 - SuXi</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* 动画背景粒子 */
        .particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(255, 255, 255, 0.6);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 1; }
            50% { transform: translateY(-20px) rotate(180deg); opacity: 0.8; }
        }

        /* 主容器 */
        .main-container {
            position: relative;
            z-index: 2;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 2rem;
        }

        /* 欢迎卡片 */
        .welcome-card {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 3rem;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            max-width: 600px;
            width: 100%;
            animation: slideUp 1s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 标题样式 */
        .title {
            font-size: 3.5rem;
            font-weight: 700;
            color: white;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.3);
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from { text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.3), 0 0 20px rgba(255, 255, 255, 0.3); }
            to { text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.3), 0 0 30px rgba(255, 255, 255, 0.5); }
        }

        .subtitle {
            font-size: 1.5rem;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 2rem;
            font-weight: 300;
        }

        .description {
            font-size: 1.1rem;
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.6;
            margin-bottom: 2.5rem;
        }

        /* 按钮组 */
        .button-group {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
            margin-bottom: 2rem;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        .btn-primary:hover {
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.6);
        }

        /* 特性列表 */
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1.5rem;
            margin-top: 2rem;
        }

        .feature {
            text-align: center;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            transition: transform 0.3s ease;
        }

        .feature:hover {
            transform: translateY(-5px);
        }

        .feature-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .feature-title {
            font-size: 1rem;
            font-weight: 600;
            color: white;
            margin-bottom: 0.5rem;
        }

        .feature-desc {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.7);
        }

        /* 时间显示 */
        .time-display {
            position: absolute;
            top: 2rem;
            right: 2rem;
            background: rgba(255, 255, 255, 0.1);
            padding: 1rem;
            border-radius: 10px;
            color: white;
            font-size: 1.1rem;
            backdrop-filter: blur(10px);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .title {
                font-size: 2.5rem;
            }

            .welcome-card {
                padding: 2rem;
                margin: 1rem;
            }

            .button-group {
                flex-direction: column;
                align-items: center;
            }

            .btn {
                width: 200px;
            }

            .time-display {
                position: static;
                margin-bottom: 2rem;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <!-- 动画背景粒子 -->
    <div class="particles" id="particles"></div>

    <!-- 时间显示 -->
    <div class="time-display" id="timeDisplay">
        <div id="currentTime"></div>
        <div style="font-size: 0.9rem; opacity: 0.8;">当前时间</div>
    </div>

    <!-- 主容器 -->
    <div class="main-container">
        <div class="welcome-card">
            <h1 class="title">欢迎来到苏溪 🌸</h1>
            <p class="subtitle">SuXi - 你的数字世界入口</p>
            <p class="description">
                这里是一个充满可能性的地方，让我们一起探索、学习和创造。
                无论你是来寻找灵感，还是想要开始新的项目，这里都将是你的起点。
            </p>

            <div class="button-group">
                <button class="btn btn-primary" onclick="showWelcomeMessage()">开始探索</button>
                <button class="btn btn-secondary" onclick="showAbout()">了解更多</button>
            </div>

            <div class="features">
                <div class="feature">
                    <div class="feature-icon">🚀</div>
                    <div class="feature-title">快速开始</div>
                    <div class="feature-desc">简单易用的界面</div>
                </div>
                <div class="feature">
                    <div class="feature-icon">💡</div>
                    <div class="feature-title">创新思维</div>
                    <div class="feature-desc">激发无限创意</div>
                </div>
                <div class="feature">
                    <div class="feature-icon">🌟</div>
                    <div class="feature-title">优质体验</div>
                    <div class="feature-desc">精心设计的交互</div>
                </div>
                <div class="feature">
                    <div class="feature-icon">🔧</div>
                    <div class="feature-title">强大工具</div>
                    <div class="feature-desc">丰富的功能集合</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 创建动画粒子
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 50;

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 6 + 's';
                particle.style.animationDuration = (Math.random() * 3 + 3) + 's';
                particlesContainer.appendChild(particle);
            }
        }

        // 更新时间显示
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            document.getElementById('currentTime').textContent = timeString;
        }

        // 欢迎消息
        function showWelcomeMessage() {
            alert('🎉 欢迎开始你的数字之旅！\n\n在这里，每一次点击都是新的开始，每一个想法都值得被实现。让我们一起创造属于你的精彩世界！');
        }

        // 关于信息
        function showAbout() {
            alert('📖 关于苏溪\n\n苏溪（SuXi）是一个现代化的数字平台，致力于为用户提供优质的在线体验。我们相信技术应该服务于人，让每个人都能轻松地实现自己的想法。\n\n✨ 特色功能：\n• 直观的用户界面\n• 响应式设计\n• 现代化的交互体验\n• 持续的功能更新');
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            createParticles();
            updateTime();
            setInterval(updateTime, 1000);

            // 添加键盘快捷键
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Enter') {
                    showWelcomeMessage();
                } else if (e.key === ' ') {
                    e.preventDefault();
                    showAbout();
                }
            });
        });

        // 添加鼠标移动效果
        document.addEventListener('mousemove', function(e) {
            const particles = document.querySelectorAll('.particle');
            const mouseX = e.clientX / window.innerWidth;
            const mouseY = e.clientY / window.innerHeight;

            particles.forEach((particle, index) => {
                const speed = (index % 5 + 1) * 0.5;
                const x = (mouseX - 0.5) * speed;
                const y = (mouseY - 0.5) * speed;
                particle.style.transform = `translate(${x}px, ${y}px)`;
            });
        });
    </script>
</body>
</html>
